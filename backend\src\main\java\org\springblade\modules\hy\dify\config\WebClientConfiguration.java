package org.springblade.modules.hy.dify.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration(proxyBeanMethods = false)
public class WebClientConfiguration {

    /**
     * WebClient 配置
     */
    @Bean
    public WebClient webClient() {
        return WebClient.builder()
                .build();
    }
}