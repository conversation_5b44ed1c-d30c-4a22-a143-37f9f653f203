import request from '@/axios';

/**
 * Dify AI对话接口
 */

/**
 * 发送对话消息（POST方式）
 * @param {Object} data - 请求数据
 * @param {string} data.query - 用户输入的问题
 * @param {string} data.conversationId - 会话ID（可选）
 * @param {Object} data.inputs - 自定义输入参数（可选）
 * @returns {Promise} API响应
 */
export const sendChatMessage = (data) => {
  return request({
    url: '/dify/chat',
    method: 'post',
    data: {
      query: data.query,
      conversationId: data.conversationId || null,
      inputs: data.inputs || {}
    }
  });
};

/**
 * 发送对话消息（GET方式）
 * @param {string} query - 用户输入的问题
 * @param {string} conversationId - 会话ID（可选）
 * @returns {Promise} API响应
 */
export const sendBlockMessage = (query, conversationId = null) => {
  return request({
    url: '/dify/block',
    method: 'get',
    params: {
      query,
      conversationId
    }
  });
};
