package org.springblade.modules.hy.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.hy.dify.pojo.dto.DifyRequestBody;
import org.springblade.modules.hy.dify.pojo.vo.BlockResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;


import org.springblade.modules.hy.dify.service.IDifyService;
import org.springframework.util.StringUtils;

/**
 * Dify服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DifyService implements IDifyService {

    @Value("${dify.url}")
    private String url;

    @Value("${dify.api-key}")
    private String apiKey;

    @Autowired
    private final WebClient webClient;

    @Override
    public BlockResponse blockingMessage(String query, Long userId, String conversationId, Map<String, Object> inputs) {
        log.info("开始调用Dify API，用户ID: {}, 查询内容: {}", userId, query);

        try {
            // 1.参数校验
            if (!StringUtils.hasText(query)) {
                throw new IllegalArgumentException("查询内容不能为空");
            }
            if (userId == null) {
                throw new IllegalArgumentException("用户ID不能为空");
            }
            if (!StringUtils.hasText(apiKey)) {
                throw new IllegalArgumentException("API密钥未配置");
            }

            // 2.设置请求体
            DifyRequestBody body = new DifyRequestBody();
            body.setInputs(inputs != null ? inputs : new HashMap<>());
            body.setQuery(query);
            body.setResponseMode("blocking");
            body.setConversationId(conversationId);
            body.setUser(userId.toString());

            log.debug("请求体: {}", JSON.toJSONString(body));

            // 3.使用webclient发送post请求
            BlockResponse response = webClient.post()
                    .uri(url + "/chat-messages")
                    .headers(httpHeaders -> {
                        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                        httpHeaders.setBearerAuth(apiKey);
                    })
                    .bodyValue(JSON.toJSONString(body))
                    .retrieve()
                    .bodyToMono(BlockResponse.class)
                    .block(); // 转换为阻塞调用

            log.info("Dify API调用成功，响应ID: {}", response != null ? response.getId() : "null");
            return response;

        } catch (Exception e) {
            log.error("调用Dify API失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            throw new RuntimeException("调用Dify API失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BlockResponse blockingMessage(String query, Long userId, String conversationId) {
        return blockingMessage(query, userId, conversationId, null);
    }

}
