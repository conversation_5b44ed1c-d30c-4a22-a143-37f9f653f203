/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.dify.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Dify阻塞模式响应体
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@Schema(description = "Dify阻塞模式响应体")
public class BlockResponse implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 事件类型，固定为 message
	 */
	@Schema(description = "事件类型，固定为 message")
	private String event;

	/**
	 * 任务ID，用于请求跟踪
	 */
	@Schema(description = "任务ID，用于请求跟踪")
	@JsonProperty("task_id")
	private String taskId;

	/**
	 * 唯一ID
	 */
	@Schema(description = "唯一ID")
	private String id;

	/**
	 * 消息唯一ID
	 */
	@Schema(description = "消息唯一ID")
	@JsonProperty("message_id")
	private String messageId;

	/**
	 * 会话ID
	 */
	@Schema(description = "会话ID")
	@JsonProperty("conversation_id")
	private String conversationId;

	/**
	 * App模式，固定为 chat
	 */
	@Schema(description = "App模式，固定为 chat")
	private String mode;

	/**
	 * 完整回复内容
	 */
	@Schema(description = "完整回复内容")
	private String answer;

	/**
	 * 元数据
	 */
	@Schema(description = "元数据")
	private Map<String, Object> metadata;

	/**
	 * 模型用量信息
	 */
	@Schema(description = "模型用量信息")
	private Usage usage;

	/**
	 * 引用和归属分段列表
	 */
	@Schema(description = "引用和归属分段列表")
	@JsonProperty("retriever_resources")
	private List<RetrieverResource> retrieverResources;

	/**
	 * 消息创建时间戳
	 */
	@Schema(description = "消息创建时间戳")
	@JsonProperty("created_at")
	private Long createdAt;

	/**
	 * 模型用量信息
	 */
	@Data
	@Schema(description = "模型用量信息")
	public static class Usage implements Serializable {
		@Serial
		private static final long serialVersionUID = 1L;

		/**
		 * 提示令牌数
		 */
		@Schema(description = "提示令牌数")
		@JsonProperty("prompt_tokens")
		private Integer promptTokens;

		/**
		 * 完成令牌数
		 */
		@Schema(description = "完成令牌数")
		@JsonProperty("completion_tokens")
		private Integer completionTokens;

		/**
		 * 总令牌数
		 */
		@Schema(description = "总令牌数")
		@JsonProperty("total_tokens")
		private Integer totalTokens;

		/**
		 * 提示单价
		 */
		@Schema(description = "提示单价")
		@JsonProperty("prompt_unit_price")
		private String promptUnitPrice;

		/**
		 * 提示价格单位
		 */
		@Schema(description = "提示价格单位")
		@JsonProperty("prompt_price_unit")
		private String promptPriceUnit;

		/**
		 * 提示价格
		 */
		@Schema(description = "提示价格")
		@JsonProperty("prompt_price")
		private String promptPrice;

		/**
		 * 完成单价
		 */
		@Schema(description = "完成单价")
		@JsonProperty("completion_unit_price")
		private String completionUnitPrice;

		/**
		 * 完成价格单位
		 */
		@Schema(description = "完成价格单位")
		@JsonProperty("completion_price_unit")
		private String completionPriceUnit;

		/**
		 * 完成价格
		 */
		@Schema(description = "完成价格")
		@JsonProperty("completion_price")
		private String completionPrice;

		/**
		 * 总价格
		 */
		@Schema(description = "总价格")
		@JsonProperty("total_price")
		private String totalPrice;

		/**
		 * 货币
		 */
		@Schema(description = "货币")
		private String currency;

		/**
		 * 延迟时间（毫秒）
		 */
		@Schema(description = "延迟时间（毫秒）")
		@JsonProperty("latency")
		private Double latency;
	}

	/**
	 * 引用和归属分段
	 */
	@Data
	@Schema(description = "引用和归属分段")
	public static class RetrieverResource implements Serializable {
		@Serial
		private static final long serialVersionUID = 1L;

		/**
		 * 位置
		 */
		@Schema(description = "位置")
		private Integer position;

		/**
		 * 数据集ID
		 */
		@Schema(description = "数据集ID")
		@JsonProperty("dataset_id")
		private String datasetId;

		/**
		 * 数据集名称
		 */
		@Schema(description = "数据集名称")
		@JsonProperty("dataset_name")
		private String datasetName;

		/**
		 * 文档ID
		 */
		@Schema(description = "文档ID")
		@JsonProperty("document_id")
		private String documentId;

		/**
		 * 文档名称
		 */
		@Schema(description = "文档名称")
		@JsonProperty("document_name")
		private String documentName;

		/**
		 * 分段ID
		 */
		@Schema(description = "分段ID")
		@JsonProperty("segment_id")
		private String segmentId;

		/**
		 * 分段内容
		 */
		@Schema(description = "分段内容")
		private String content;

		/**
		 * 相似度分数
		 */
		@Schema(description = "相似度分数")
		private Double score;
	}

}
