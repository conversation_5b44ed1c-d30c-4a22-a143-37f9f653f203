/**
 * 移动端认证测试工具
 * 用于开发和调试移动端登录功能
 */

// 创建全局测试对象
window.mobileAuthTest = {
  /**
   * 模拟登录
   */
  login() {
    const mockUserInfo = {
      access_token: 'mock_token_' + Date.now(),
      user_id: 'test_user_' + Math.random().toString(36).substr(2, 9),
      user_name: '测试用户',
      account: 'testuser',
      source: 'dingtalk',
      authCode: 'mock_auth_code_' + Date.now(),
      dingUserId: 'ding_user_' + Math.random().toString(36).substr(2, 9),
      avatar: '',
      email: '<EMAIL>',
      phone: '***********',
      loginTime: new Date().toISOString()
    };
    
    localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
    console.log('✅ 模拟登录成功:', mockUserInfo);
    return mockUserInfo;
  },

  /**
   * 模拟登出
   */
  logout() {
    localStorage.removeItem('userInfo');
    console.log('✅ 模拟登出成功');
    return true;
  },

  /**
   * 检查登录状态
   */
  check() {
    try {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const parsed = JSON.parse(userInfo);
        const isValid = !!(parsed.access_token && parsed.user_id);
        console.log('🔍 登录状态检查:', isValid ? '已登录' : '未登录');
        console.log('📋 用户信息:', parsed);
        return { isLoggedIn: isValid, userInfo: parsed };
      } else {
        console.log('🔍 登录状态检查: 未登录');
        return { isLoggedIn: false, userInfo: null };
      }
    } catch (e) {
      console.error('❌ 检查登录状态失败:', e);
      return { isLoggedIn: false, userInfo: null, error: e.message };
    }
  },

  /**
   * 运行完整测试
   */
  test() {
    console.log('🧪 开始移动端认证测试...');
    
    // 1. 检查初始状态
    console.log('\n1️⃣ 检查初始状态:');
    this.check();
    
    // 2. 执行登录
    console.log('\n2️⃣ 执行模拟登录:');
    this.login();
    
    // 3. 验证登录状态
    console.log('\n3️⃣ 验证登录状态:');
    this.check();
    
    // 4. 执行登出
    console.log('\n4️⃣ 执行登出:');
    this.logout();
    
    // 5. 验证登出状态
    console.log('\n5️⃣ 验证登出状态:');
    this.check();
    
    console.log('\n✅ 移动端认证测试完成!');
  },

  /**
   * 清理所有数据
   */
  clear() {
    localStorage.removeItem('userInfo');
    console.log('🧹 已清理所有认证数据');
  },

  /**
   * 设置自定义用户信息
   */
  setUser(userInfo) {
    const defaultInfo = {
      access_token: 'custom_token_' + Date.now(),
      user_id: 'custom_user_' + Math.random().toString(36).substr(2, 9),
      user_name: '自定义用户',
      account: 'customuser',
      source: 'dingtalk',
      loginTime: new Date().toISOString()
    };
    
    const finalUserInfo = { ...defaultInfo, ...userInfo };
    localStorage.setItem('userInfo', JSON.stringify(finalUserInfo));
    console.log('✅ 已设置自定义用户信息:', finalUserInfo);
    return finalUserInfo;
  },

  /**
   * 获取帮助信息
   */
  help() {
    console.log(`
🛠️ 移动端认证测试工具帮助

可用命令:
- mobileAuthTest.login()     // 模拟登录
- mobileAuthTest.logout()    // 模拟登出  
- mobileAuthTest.check()     // 检查登录状态
- mobileAuthTest.test()      // 运行完整测试
- mobileAuthTest.clear()     // 清理所有数据
- mobileAuthTest.setUser(userInfo) // 设置自定义用户信息
- mobileAuthTest.help()      // 显示帮助信息

示例:
mobileAuthTest.setUser({
  user_name: '张三',
  account: 'zhangsan',
  email: '<EMAIL>'
});
    `);
  }
};

console.log('🔧 移动端认证测试工具已加载');
console.log('💡 输入 mobileAuthTest.help() 查看帮助信息');

export default window.mobileAuthTest;
