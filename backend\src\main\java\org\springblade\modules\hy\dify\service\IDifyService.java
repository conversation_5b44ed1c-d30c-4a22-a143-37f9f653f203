/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.dify.service;

import org.springblade.modules.hy.dify.pojo.vo.BlockResponse;

import java.util.Map;

/**
 * Dify服务接口
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface IDifyService {

	/**
	 * 阻塞式调用Dify
	 *
	 * @param query          查询文本
	 * @param userId         用户ID
	 * @param conversationId 会话ID（可选）
	 * @param inputs         输入参数（可选）
	 * @return BlockResponse
	 */
	BlockResponse blockingMessage(String query, Long userId, String conversationId, Map<String, Object> inputs);

	/**
	 * 阻塞式调用Dify（简化版本）
	 *
	 * @param query          查询文本
	 * @param userId         用户ID
	 * @param conversationId 会话ID（可选）
	 * @return BlockResponse
	 */
	BlockResponse blockingMessage(String query, Long userId, String conversationId);

}
