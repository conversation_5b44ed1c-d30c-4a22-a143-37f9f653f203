/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.dify.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * Dify请求体
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@Schema(description = "Dify请求体")
public class DifyRequestBody implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户输入/提问内容
	 */
	@Schema(description = "用户输入/提问内容", required = true)
	private String query;

	/**
	 * 允许传入 App 定义的各变量值
	 */
	@Schema(description = "允许传入 App 定义的各变量值")
	private Map<String, Object> inputs;

	/**
	 * 响应模式
	 * streaming 流式模式（推荐）
	 * blocking 阻塞模式
	 */
	@Schema(description = "响应模式：streaming 流式模式，blocking 阻塞模式", allowableValues = {"streaming", "blocking"})
	@JsonProperty("response_mode")
	private String responseMode;

	/**
	 * 用户标识
	 */
	@Schema(description = "用户标识，用于定义终端用户的身份")
	private String user;

	/**
	 * 会话ID
	 */
	@Schema(description = "会话ID，需要基于之前的聊天记录继续对话时传入")
	@JsonProperty("conversation_id")
	private String conversationId;

}
